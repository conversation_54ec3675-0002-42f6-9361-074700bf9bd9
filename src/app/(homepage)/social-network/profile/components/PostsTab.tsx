"use client";

import { CreatePostForm, PostContent } from "./posts";
// Import các component con
import { useCallback, useEffect, useRef, useState } from "react";

import usePostStore from "@/store/postStore";

import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { Post } from "@/services/social-network/types/types";
import { toast } from "react-toastify";
import type { UserData } from "../page";

interface PostsTabProps {
  userData: UserData;
}

// Mở rộng kiểu Post để thêm avatarUrl và fileUrls
export type ExtendedPost = Post & {
  avatarUrl?: string;
  fileUrls?: string[];
  orgName?: string;
  postRefer?: ExtendedPost | null;
};

const DEFAULT_SIZE = 5;

const PostsTab = ({ userData }: PostsTabProps) => {
  // States cho component
  const [posts, setPosts] = useState<ExtendedPost[]>([]);
  const [editingPostId, setEditingPostId] = useState<number | null>(null);
  const [page, setPage] = useState(0);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const observerTarget = useRef<HTMLDivElement>(null);
  const { viewFile } = useUploadFile();
  const [initUI, setInitUI] = useState(true);
  
  // Xử lý trạng thái chỉnh sửa từ PostContent
  const handleEditStateChange = (isEditing: boolean, postId: number) => {
    setEditingPostId(isEditing ? postId : null);
  };

  // Lấy danh sách bài đăng
  const fetchPosts = async (
    type: "reset" | "infinite",
    page: number = 0,
    size: number = DEFAULT_SIZE
  ) => {
    setLoading(true);
    try {
      const response = await socialNetworkServices.getListPostSocialNetwork({
        page,
        size
      });

      if (response.data?.data?.content) {
        // Lấy danh sách bài đăng từ API
        const postsData = response.data.data.content;

        // Xử lý lấy URL cho thumbnail và files của mỗi bài đăng
        const postsWithUrls = await Promise.all(
          postsData.map(async (post: Post) => {
            let avatarUrl = undefined;
            let fileUrls: string[] = [];
            let postReferWithUrls = null;

            // Nếu có thumbnail, gọi API viewFile để lấy URL
            if (post.thumbnail) {
              try {
                avatarUrl = (await viewFile(
                  post.thumbnail,
                  "social-network"
                )) as string;
              } catch (error) {
                console.error("Lỗi khi lấy URL cho thumbnail:", error);
              }
            }

            // Nếu có files, gọi API viewFile để lấy URL cho từng file
            if (post.files && post.files.length > 0) {
              try {
                // Gọi API viewFile cho từng fileId trong mảng files
                const filePromises = post.files.map((fileId) =>
                  viewFile(fileId, "social-network")
                );
                // Ép kiểu kết quả trả về thành string[]
                fileUrls = (await Promise.all(filePromises)) as string[];
              } catch (error) {
                console.error("Lỗi khi lấy URL cho files:", error);
              }
            }

            // Xử lý postRefer nếu có
            if (post.postRefer && post.referPostAvailable) {
              try {
                // Xử lý avatar của postRefer
                let referAvatarUrl = undefined;
                if (post.postRefer.thumbnail) {
                  referAvatarUrl = (await viewFile(
                    post.postRefer.thumbnail,
                    "social-network"
                  )) as string;
                }

                // Xử lý files của postRefer
                let referFileUrls: string[] = [];
                if (post.postRefer.files && post.postRefer.files.length > 0) {
                  const referFilePromises = post.postRefer.files.map((fileId) =>
                    viewFile(fileId, "social-network")
                  );
                  referFileUrls = (await Promise.all(
                    referFilePromises
                  )) as string[];
                }

                // Tạo postRefer với các URL đã xử lý
                postReferWithUrls = {
                  ...post.postRefer,
                  avatarUrl: referAvatarUrl,
                  fileUrls: referFileUrls
                };
              } catch (error) {
                console.error("Lỗi khi xử lý postRefer:", error);
              }
            }

            // Trả về post với thêm trường avatarUrl, fileUrls và postRefer đã xử lý
            return {
              ...post,
              avatarUrl,
              fileUrls,
              postRefer: postReferWithUrls
            };
          })
        );

        // Cập nhật state với danh sách bài đăng đã có avatarUrl và fileUrls
        type === "infinite"
          ? setPosts([...posts, ...postsWithUrls])
          : setPosts([...postsWithUrls]);
        setHasMore(!response.data.data.last);
      }
    } catch (error) {
      console.error("Lỗi khi tải bài đăng:", error);
      toast.error("Không thể tải bài đăng");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts("reset").then(finishInitUI);
  }, []);



  // Xử lý infinite scroll
  const loadMorePosts = useCallback(() => {
    if (loading || !hasMore) return;
    setPage(page + 1);
    fetchPosts("infinite", page + 1);
  }, [hasMore, loading]);

  const finishInitUI = () => {
    setInitUI(false);
  };

  // Lấy resetCounter từ store
  const { resetCounter } = usePostStore();
  
  // Gọi API lấy danh sách bài đăng khi component được mount hoặc userData thay đổi
  useEffect(() => {
    setPage(0);
    fetchPosts("reset").then(() => {
      setInitUI(false); // Đặt initUI thành false sau khi tải dữ liệu xong
    });
  }, [userData]); // Sử dụng userData làm dependency để khi người dùng thay đổi, danh sách bài đăng cũng được cập nhật
  
  // Theo dõi thay đổi của resetCounter để cập nhật lại danh sách bài đăng
  useEffect(() => {
    if (resetCounter > 0) {
      // Khi resetCounter thay đổi, cập nhật lại danh sách bài đăng
      fetchPosts("reset", 0, DEFAULT_SIZE * (page + 1));
    }
  }, [resetCounter, page]);

  // Thiết lập Intersection Observer cho infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMorePosts();
        }
      },
      { threshold: 1.0 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [loadMorePosts]);

  return (
    <div className="space-y-6">
      {/* Form tạo bài đăng mới (luôn hiển thị nhưng vô hiệu hóa khi đang chỉnh sửa) */}

      {!initUI && (
        <div className={editingPostId ? "pointer-events-none opacity-50" : ""}>
          <CreatePostForm
            userData={userData}
            resetList={() => {
              setPage(0);
              fetchPosts("reset");
            }}
          />
        </div>
      )}

      {/* Danh sách bài đăng */}
      {posts.map((post, index) => (
        <div key={post.postSocialNetworkId || index}>
          {/* Nếu đang chỉnh sửa bài đăng này, hiển thị form chỉnh sửa */}
          <PostContent
            post={post}
            resetList={() => {
              fetchPosts("reset", 0, DEFAULT_SIZE * (page + 1));
            }}
            onEditStateChange={handleEditStateChange}
          />
        </div>
      ))}

      {/* Hiển thị khi không có bài đăng nào */}
      {posts.length === 0 && !loading && !initUI && (
        <div className="rounded-lg bg-white p-8 text-center shadow-md">
          <div className="mb-2 text-gray-500">Chưa có bài đăng nào</div>
          <p className="text-gray-600">
            Đăng bài viết đầu tiên của bạn để chia sẻ với cộng đồng!
          </p>
        </div>
      )}

      {/* Loading indicator & observer target */}
      {hasMore && (
        <div ref={observerTarget} className="py-4 text-center">
          {loading && (
            <div className="flex items-center justify-center space-x-2">
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "0ms" }}
              ></div>
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "150ms" }}
              ></div>
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "300ms" }}
              ></div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PostsTab;
