"use client";

import { useEffect, useState } from "react";

import History from "@/components/History";
import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { UserSocialNetwork } from "@/services/social-network/types/types";
import FriendsTab from "./components/FriendsTab";
import PasswordTab from "./components/PasswordTab";
import PostsTab from "./components/PostsTab";
import ProfileTabs from "./components/ProfileTabs";
import SavedTab from "./components/SavedTab";
import UserProfileHeader from "./components/UserProfileHeader";

export type UserData = Partial<UserSocialNetwork> & {
  avatarUrl?: string;
  avatarFile?: File;
  coverUrl?: string;
  coverFile?: File;
};

export default function ProfilePage() {
  // States cho trang profile
  const [activeTab, setActiveTab] = useState("posts");
  const [userData, setUserData] = useState<UserData>({});
  const [loadingUserData, setLoadingUserData] = useState(true);
  const { viewFile } = useUploadFile();

  // Gọi API để lấy dữ liệu người dùng
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoadingUserData(true);
        const response = await socialNetworkServices.getUserSocialNetwork();
        const userSocialNetwork: UserSocialNetwork = response.data.data;
        let avatarUrl = "";
        let coverUrl = "";
        // Lấy URL cho avatar và cover photo
        if (userSocialNetwork.thumbnail) {
          try {
            const thumbnailUrl = await viewFile(
              userSocialNetwork.thumbnail,
              "social-network"
            );
            avatarUrl = thumbnailUrl as string;
          } catch (error) {
            console.error("Lỗi khi lấy URL cho avatar:", error);
          }
        }

        if (userSocialNetwork.background) {
          try {
            const backgroundUrl = await viewFile(
              userSocialNetwork.background,
              "social-network"
            );
            coverUrl = backgroundUrl as string;
          } catch (error) {
            console.error("Lỗi khi lấy URL cho cover photo:", error);
          }
        }
        setUserData({
          ...userSocialNetwork,
          avatarUrl,
          coverUrl
        });
      } catch (error) {
        console.error("Lỗi khi lấy dữ liệu người dùng:", error);
      } finally {
        setLoadingUserData(false);
      }
    };

    fetchUserData();
  }, []);
  return (
    <div className="min-h-screen bg-gray-50 pb-10">
      <UserProfileHeader
        userData={userData}
        loadingUserData={loadingUserData}
        setUserData={setUserData}
      />
      <div className="container relative mx-auto px-4">
        <div className="flex flex-col gap-6 lg:flex-row lg:items-start">
          <History className="self-start overflow-y-auto lg:sticky lg:top-20 w-full lg:w-1/4 px-2 z-40" />
          <div className="w-full lg:w-3/4">
            <ProfileTabs
              activeTab={activeTab}
              onTabChange={setActiveTab}
              tabs={[
                { id: "posts", label: "Bài viết" },
                { id: "friends", label: "Bạn bè" },
                { id: "saved", label: "Đã lưu" },
                // { id: "photos", label: "Ảnh" },
                { id: "password", label: "Đổi mật khẩu" }
              ]}
            />
            {activeTab === "posts" && (
              <PostsTab userData={userData} />
            )}
            {activeTab === "friends" && <FriendsTab />}
            {activeTab === "saved" && <SavedTab />}
            {/* {activeTab === "photos" && <PhotosTab />} */}
            {activeTab === "password" && <PasswordTab />}
          </div>
        </div>
      </div>
    </div>
  );
}
