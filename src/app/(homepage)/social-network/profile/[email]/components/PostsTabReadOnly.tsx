"use client";

import { useCallback, useEffect, useRef, useState } from "react";

import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { Post } from "@/services/social-network/types/types";
import { toast } from "react-toastify";
import type { UserData } from "../page";
import PostContentReadOnly from "./PostContentReadOnly";

interface PostsTabReadOnlyProps {
  userData: UserData;
}

// Mở rộng kiểu Post để thêm avatarUrl và fileUrls
export type ExtendedPost = Post & {
  avatarUrl?: string;
  fileUrls?: string[];
  orgName?: string;
  postRefer?: ExtendedPost | null;
};

const DEFAULT_SIZE = 5;

const PostsTabReadOnly = ({ userData }: PostsTabReadOnlyProps) => {
  // States cho component
  const [posts, setPosts] = useState<ExtendedPost[]>([]);
  const [page, setPage] = useState(0);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const observerTarget = useRef<HTMLDivElement>(null);
  const { viewFile } = useUploadFile();
  const [initUI, setInitUI] = useState(true);
  const [initialized, setInitialized] = useState(false);

  // Lấy danh sách bài đăng của user theo email
  const fetchPosts = async (
    type: "reset" | "infinite",
    page: number = 0,
    size: number = DEFAULT_SIZE
  ) => {
    setLoading(true);
    try {
      // Gọi API lấy bài đăng của user theo username (email)
      const response =
        await socialNetworkServices.getListPostSocialNetworkByUserName({
          username: userData.email || "",
          page,
          size
        });

      if (response.data?.data?.content) {
        // Lấy danh sách bài đăng từ API
        const postsData = response.data.data.content;

        // Xử lý lấy URL cho thumbnail và files của mỗi bài đăng
        const postsWithUrls = await Promise.all(
          postsData.map(async (post: Post) => {
            let avatarUrl = undefined;
            let fileUrls: string[] = [];
            let postReferWithUrls = null;

            // Nếu có thumbnail, gọi API viewFile để lấy URL
            if (post.thumbnail) {
              try {
                avatarUrl = (await viewFile(
                  post.thumbnail,
                  "social-network"
                )) as string;
              } catch (error) {
                console.error("Lỗi khi lấy URL cho thumbnail:", error);
              }
            }

            // Nếu có files, gọi API viewFile để lấy URL cho từng file
            if (post.files && post.files.length > 0) {
              try {
                // Gọi API viewFile cho từng fileId trong mảng files
                const filePromises = post.files.map((fileId) =>
                  viewFile(fileId, "social-network")
                );
                // Ép kiểu kết quả trả về thành string[]
                fileUrls = (await Promise.all(filePromises)) as string[];
              } catch (error) {
                console.error("Lỗi khi lấy URL cho files:", error);
              }
            }

            // Xử lý postRefer nếu có
            if (post.postRefer && post.referPostAvailable) {
              try {
                // Xử lý avatar của postRefer
                let referAvatarUrl = undefined;
                if (post.postRefer.thumbnail) {
                  referAvatarUrl = (await viewFile(
                    post.postRefer.thumbnail,
                    "social-network"
                  )) as string;
                }

                // Xử lý files của postRefer
                let referFileUrls: string[] = [];
                if (post.postRefer.files && post.postRefer.files.length > 0) {
                  const referFilePromises = post.postRefer.files.map((fileId) =>
                    viewFile(fileId, "social-network")
                  );
                  referFileUrls = (await Promise.all(
                    referFilePromises
                  )) as string[];
                }

                // Tạo postRefer với các URL đã xử lý
                postReferWithUrls = {
                  ...post.postRefer,
                  avatarUrl: referAvatarUrl,
                  fileUrls: referFileUrls
                };
              } catch (error) {
                console.error("Lỗi khi xử lý postRefer:", error);
              }
            }

            // Trả về post với thêm trường avatarUrl, fileUrls và postRefer đã xử lý
            return {
              ...post,
              avatarUrl,
              fileUrls,
              postRefer: postReferWithUrls
            };
          })
        );

        // Cập nhật state với danh sách bài đăng đã có avatarUrl và fileUrls
        type === "infinite"
          ? setPosts([...posts, ...postsWithUrls])
          : setPosts([...postsWithUrls]);
        setHasMore(!response.data.data.last);
      }
    } catch (error) {
      console.error("Lỗi khi tải bài đăng:", error);
      toast.error("Không thể tải bài đăng");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userData.email && !initialized) {
      setInitialized(true);
      fetchPosts("reset").then(() => setInitUI(false));
    }
  }, [userData.email, initialized]);

  // Xử lý infinite scroll
  const loadMorePosts = useCallback(() => {
    if (loading || !hasMore || !userData.email) return;
    setPage(prevPage => {
      const nextPage = prevPage + 1;
      fetchPosts("infinite", nextPage);
      return nextPage;
    });
  }, [hasMore, loading, userData.email]);

  // Thiết lập Intersection Observer cho infinite scroll
  useEffect(() => {
    if (!initialized) return; // Chỉ setup observer sau khi đã initialized

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !loading && hasMore) {
          loadMorePosts();
        }
      },
      { threshold: 0.1, rootMargin: '20px' }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [loadMorePosts, initialized, loading, hasMore]);

  return (
    <div className="space-y-6">
      {/* Danh sách bài đăng */}
      {posts.map((post, index) => (
        <div key={post.postSocialNetworkId || index}>
          <PostContentReadOnly post={post} />
        </div>
      ))}

      {/* Hiển thị khi không có bài đăng nào */}
      {posts.length === 0 && !loading && !initUI && (
        <div className="rounded-lg bg-white p-8 text-center shadow-md">
          <div className="mb-2 text-gray-500">Chưa có bài đăng nào</div>
          <p className="text-gray-600">
            Người dùng này chưa đăng bài viết nào.
          </p>
        </div>
      )}

      {/* Loading indicator & observer target */}
      {hasMore && (
        <div ref={observerTarget} className="py-4 text-center">
          {loading && (
            <div className="flex items-center justify-center space-x-2">
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "0ms" }}
              ></div>
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "150ms" }}
              ></div>
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "300ms" }}
              ></div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PostsTabReadOnly;
