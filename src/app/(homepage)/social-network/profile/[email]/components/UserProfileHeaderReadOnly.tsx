"use client";

import {
  FaBriefcase,
  FaBuilding,
  FaCalendarAlt,
  FaEnvelope,
  FaGraduationCap,
  FaMapMarkerAlt,
  FaPhone,
  FaVenusMars
} from "react-icons/fa";
import { MdVerified } from "react-icons/md";
import { RiBuilding2Line } from "react-icons/ri";

import {
  formatDateForDisplay
} from "../../../utils/dateUtils";

import DefaultAvatar from "@/components/ui/DefaultAvatar";
import DefaultCover from "@/components/ui/DefaultCover";
import FriendServices from "@/services/social-network/friendServices";
import Image from "next/image";
import { useEffect, useState } from "react";
import { FaClock, FaUserPlus, FaUsers } from "react-icons/fa";
import type { UserData } from "../page";

// Helper function để hiển thị trạng thái kết bạn
const getFriendStatusDisplay = (status: string) => {
  switch (status) {
    case "FRIEND":
      return {
        text: "Bạn bè",
        color: "text-emerald-700",
        bgColor: "bg-emerald-50",
        borderColor: "border-emerald-200",
        icon: FaUsers,
      };
    case "REQUESTED":
      return {
        text: "Đang chờ",
        color: "text-amber-700",
        bgColor: "bg-amber-50",
        borderColor: "border-amber-200",
        icon: FaClock,
      };
    case "NOT_FRIEND":
      return {
        text: "Chưa kết bạn",
        color: "text-slate-600",
        bgColor: "bg-slate-50",
        borderColor: "border-slate-200",
        icon: FaUserPlus,
      };
    default:
      return {
        text: "Không xác định",
        color: "text-slate-600",
        bgColor: "bg-slate-50",
        borderColor: "border-slate-200",
        icon: FaUsers,
      };
  }
};

interface UserProfileHeaderReadOnlyProps {
  userData: UserData;
  loadingUserData: boolean;
}

export default function UserProfileHeaderReadOnly({
  userData,
  loadingUserData
}: UserProfileHeaderReadOnlyProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [currentFriendStatus, setCurrentFriendStatus] = useState(userData.friendStatus || "NOT_FRIEND");

  const friendStatus = getFriendStatusDisplay(currentFriendStatus);
  const IconComponent = friendStatus.icon;

  useEffect(() => {
    setCurrentFriendStatus(userData.friendStatus || "NOT_FRIEND");
  }, [userData]);

  // Hàm gửi lời mời kết bạn
  const handleSendFriendRequest = async () => {
    if (!userData.email || isLoading) return;

    setIsLoading(true);
    try {
      await FriendServices.sendFriendRequest(userData.email);
      setCurrentFriendStatus("REQUESTED");
      alert("Đã gửi lời mời kết bạn thành công!");
    } catch (error) {
      console.error("Lỗi khi gửi lời mời kết bạn:", error);
      alert("Có lỗi xảy ra khi gửi lời mời kết bạn!");
    } finally {
      setIsLoading(false);
    }
  };

  if (loadingUserData)
    return (
      <>
        {/* Cover Photo Skeleton */}
        <div className="relative h-64 w-full animate-pulse overflow-hidden bg-gray-200 md:h-80"></div>

        {/* Profile Header Skeleton */}
        <div className="container relative mx-auto px-4">
          <div className="-mt-20 mb-6 rounded-lg bg-white p-6 shadow-md md:p-8">
            <div className="flex flex-col items-center md:items-start gap-6 md:flex-row">
              {/* Avatar Skeleton */}
              <div className="relative">
                <div className="relative h-32 w-32 animate-pulse overflow-hidden rounded-full border-4 border-white bg-gray-300 md:h-40 md:w-40"></div>
              </div>

              {/* User Info Skeleton */}
              <div className="flex-1">
                <div className="flex w-full flex-col-reverse justify-between gap-4 md:flex-row md:items-center">
                  <div>
                    {/* Name Skeleton */}
                    <div className="mb-2 h-8 w-48 animate-pulse rounded-md bg-gray-300"></div>
                    {/* Role Skeleton */}
                    <div className="h-4 w-32 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                </div>

                {/* Bio Skeleton */}
                <div className="mt-4">
                  <div className="h-16 w-full animate-pulse rounded-md bg-gray-300"></div>
                </div>

                {/* User Details Skeleton */}
                <div className="mt-4 flex flex-wrap gap-x-6 gap-y-2">
                  <div className="h-6 w-32 animate-pulse rounded-md bg-gray-300"></div>
                  <div className="h-6 w-32 animate-pulse rounded-md bg-gray-300"></div>
                  <div className="h-6 w-32 animate-pulse rounded-md bg-gray-300"></div>
                </div>

                {/* Stats Skeleton */}
                <div className="mt-6 flex flex-wrap gap-6">
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );

  return (
    <>
      {/* Cover Image */}
      <div className="relative h-64 w-full overflow-hidden md:h-80">
        {userData.coverUrl ? (
          <Image
            src={userData.coverUrl}
            alt="Cover"
            fill
            className="object-cover"
          />
        ) : (
          <DefaultCover height="100%" />
        )}
      </div>

      {/* Profile Header */}
      <div className="container relative mx-auto px-4">
        <div className="-mt-20 mb-6 rounded-lg bg-white p-6 shadow-md md:p-8">
          <div className="flex flex-col items-center md:items-start gap-6 md:flex-row">
            {/* Avatar */}
            <div className="relative">
              <div className="relative h-32 w-32 overflow-hidden rounded-full border-4 border-white md:h-40 md:w-40">
                {userData.avatarUrl ? (
                  <Image
                    src={userData.avatarUrl}
                    alt={
                      userData.fullName ||
                      `${userData.firstName} ${userData.lastName}`
                    }
                    fill
                    className="object-cover"
                  />
                ) : (
                  <DefaultAvatar
                    name={
                      userData.fullName ||
                      `${userData.firstName} ${userData.lastName}` ||
                      "User"
                    }
                    size={200}
                  />
                )}
              </div>
            </div>

            {/* User Info */}
            <div className="flex-1 relative">
              <div className="flex w-full flex-col-reverse justify-between gap-4 md:flex-row md:items-center">
                <div>
                  <h1 className="flex text-2xl font-bold md:text-3xl">
                    {userData.fullName ||
                      `${userData.firstName} ${userData.lastName}`}
                    {userData.orgName && (
                      <div className="ml-2 flex items-center rounded text-xl text-blue-600">
                        <MdVerified />
                        <div className="ml-2 flex items-center rounded-full bg-green-500 px-3 py-1 text-sm font-medium text-white shadow-sm">
                          <RiBuilding2Line></RiBuilding2Line>
                          <span className="ml-1"> {userData.orgName}</span>
                        </div>
                      </div>
                    )}
                  </h1>
                  <p className="font-medium text-blue-600">{userData.job}</p>
                </div>

                {/* Trạng thái kết bạn và nút action */}
                <div className="flex flex-col gap-4 items-end absolute top-0 right-0">
                  {/* Hiển thị trạng thái kết bạn với design đẹp */}
                  <div className="relative">
                    <div className={`inline-flex items-center gap-3 px-4 py-3 rounded-2xl border ${friendStatus.borderColor} ${friendStatus.bgColor} shadow-sm backdrop-blur-sm`}>

                      {/* Icon */}
                      <IconComponent className={`text-base ${friendStatus.color}`} />

                      {/* Status text */}
                      <span className={`text-sm font-semibold ${friendStatus.color} tracking-wide`}>
                        {friendStatus.text}
                      </span>
                    </div>
                  </div>

                  {/* Nút gửi lời mời kết bạn khi chưa kết bạn */}
                  {currentFriendStatus === "NOT_FRIEND" && (
                    <button
                      onClick={handleSendFriendRequest}
                      disabled={isLoading}
                      className="group relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-2xl font-semibold text-sm shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    >
                      {/* Button content */}
                      <div className="relative flex items-center justify-center gap-2">
                        <FaUserPlus className={`text-sm transition-transform duration-200 ${isLoading ? 'animate-pulse' : 'group-hover:scale-110'}`} />
                        <span>{isLoading ? "Đang gửi..." : "Gửi lời mời kết bạn"}</span>
                      </div>

                      {/* Loading spinner overlay */}
                      {isLoading && (
                        <div className="absolute inset-0 flex items-center justify-center bg-blue-600 bg-opacity-20">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        </div>
                      )}
                    </button>
                  )}
                </div>
              </div>

              {/* User Bio */}
              <div className="mt-4">
                <p className="text-gray-700">{userData.bio}</p>
              </div>

              {/* User Details */}
              <div className="mt-4 flex flex-wrap gap-x-6 gap-y-2 text-gray-600">
                {userData.address && (
                  <div className="flex items-center gap-2">
                    <FaMapMarkerAlt className="text-gray-500" />
                    <span>{userData.address}</span>
                  </div>
                )}
                {userData.education && (
                  <div className="flex items-center gap-2">
                    <FaGraduationCap className="text-gray-500" />
                    <span>{userData.education}</span>
                  </div>
                )}
                {userData.job && (
                  <div className="flex items-center gap-2">
                    <FaBriefcase className="text-gray-500" />
                    <span>{userData.job}</span>
                  </div>
                )}
                {userData.phoneNumber && (
                  <div className="flex items-center gap-2">
                    <FaPhone className="text-gray-500" />
                    <span>{userData.phoneNumber}</span>
                  </div>
                )}
                {userData.dateOfBirth && (
                  <div className="flex items-center gap-2">
                    <FaCalendarAlt className="text-gray-500" />
                    <span>{formatDateForDisplay(userData.dateOfBirth)}</span>
                  </div>
                )}
                {userData.gender && (
                  <div className="flex items-center gap-2">
                    <FaVenusMars className="text-gray-500" />
                    <span>{userData.gender}</span>
                  </div>
                )}
                {userData.orgName && (
                  <div className="flex items-center gap-2">
                    <FaBuilding className="text-gray-500" />
                    <span>{userData.orgName}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <FaEnvelope className="text-gray-500" />
                  <span>{userData.email}</span>
                </div>
              </div>

              {/* Stats */}
              <div className="mt-6 flex flex-wrap gap-6">
                <div className="text-center">
                  <div className="text-xl font-bold">
                    {userData.numberOfPost}
                  </div>
                  <div className="text-sm text-gray-600">Bài viết</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">
                    {userData.numberOfFriend}
                  </div>
                  <div className="text-sm text-gray-600">Bạn bè</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">
                    {userData.numberOfLike}
                  </div>
                  <div className="text-sm text-gray-600">Lượt thích</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
