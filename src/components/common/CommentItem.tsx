"use client";

import Image from "next/image";
import { useState } from "react";
import { FaPaperPlane } from "react-icons/fa";
import { toast } from "react-toastify";

import DefaultAvatar from "@/components/ui/DefaultAvatar";
import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { formatDateForDisplay } from "../../app/(homepage)/social-network/utils/dateUtils";

// Định nghĩa kiểu dữ liệu cho Comment
export type Comment = {
  commentPostSnId: number;
  content: string;
  createdAt: string;
  updatedAt: string;
  firstName: string;
  lastName: string;
  avatarUrl?: string;
  likeCount: number;
  isLiked: boolean;
  username: boolean;
  commentParentId?: number;
  childComments?: Comment[];
};

interface CommentItemProps {
  comment: Comment;
  postId: number;
  onLike: (commentId: number) => Promise<void>;
  onUnlike: (commentId: number) => Promise<void>;
  onDelete: (commentId: number) => Promise<void>;
  onReplyAdded: () => void;
  onCommentUpdated: () => void; // Callback khi comment được cập nhật (thích, xóa)
}

const CommentItem = ({ 
  comment, 
  postId, 
  onLike, 
  onUnlike, 
  onDelete,
  onReplyAdded,
  onCommentUpdated 
}: CommentItemProps) => {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [replyContent, setReplyContent] = useState("");
  const [replyLoading, setReplyLoading] = useState(false);
  const [childComments, setChildComments] = useState<Comment[]>(comment.childComments || []);
  const [showChildComments, setShowChildComments] = useState(false);
  const [childCommentsLoading, setChildCommentsLoading] = useState(false);
  const [childCommentsPage, setChildCommentsPage] = useState(0);
  const [hasMoreChildComments, setHasMoreChildComments] = useState(true);
  const { viewFile } = useUploadFile();

  // Lấy danh sách comment con
  const fetchChildComments = async (pageIndex: number) => {
    if (childCommentsLoading) return;
    setChildCommentsLoading(true);
    
    try {
      const response = await socialNetworkServices.getListChildCommentPostSocialNetwork({
        page: pageIndex,
        size: 5,
        sort: "ASC",
        postSocialNetworkId: postId,
        commentParentId: comment.commentPostSnId
      });

      if (response.data?.data?.content) {
        const commentsData = response.data.data.content;
        
        // Xử lý lấy URL cho avatar của mỗi bình luận
        const commentsWithAvatars = await Promise.all(
          commentsData.map(async (childComment: any) => {
            let avatarUrl = undefined;
            
            if (childComment.thumbnail) {
              try {
                avatarUrl = await viewFile(childComment.thumbnail, "social-network") as string;
              } catch (error) {
                console.error("Lỗi khi lấy URL cho avatar:", error);
              }
            }
            
            return {
              ...childComment,
              avatarUrl
            };
          })
        );
        
        setChildComments(pageIndex === 0 ? commentsWithAvatars : [...childComments, ...commentsWithAvatars]);
        setHasMoreChildComments(!response.data.data.last);
        if (!response.data.data.last) {
          setChildCommentsPage(pageIndex + 1);
        }
      }
    } catch (error) {
      console.error("Lỗi khi lấy danh sách bình luận con:", error);
    } finally {
      setChildCommentsLoading(false);
    }
  };

  // Xử lý hiển thị/ẩn form trả lời và comment con
  const toggleReplyForm = () => {
    // Đảo ngược trạng thái hiển thị comment con
    const newShowChildComments = !showChildComments;
    setShowChildComments(newShowChildComments);
    
    // Nếu đang mở comment con, gọi API để lấy dữ liệu mới nhất
    if (newShowChildComments) {
      setChildCommentsPage(0);
      fetchChildComments(0);
      
      // Mở form trả lời khi mở comment con
      setShowReplyForm(true);
    } else {
      // Đóng form trả lời khi đóng comment con
      setShowReplyForm(false);
    }
  };

  // Xử lý thêm trả lời comment
  const handleAddReply = async () => {
    if (!replyContent.trim() || replyLoading) return;
    setReplyLoading(true);
    
    try {
      await socialNetworkServices.createCommentPostSocialNetwork({
        postSocialNetworkId: postId,
        content: replyContent,
        commentParentId: comment.commentPostSnId,
        imageId: ""
      });
      
      // Reset content và tải lại danh sách bình luận con
      setReplyContent("");
      setChildCommentsPage(0);
      await fetchChildComments(0);
      setShowChildComments(true);
      onReplyAdded(); // Thông báo cho component cha
      toast.success("Đã thêm trả lời");
    } catch (error) {
      console.error("Lỗi khi thêm trả lời:", error);
      toast.error("Không thể thêm trả lời");
    } finally {
      setReplyLoading(false);
    }
  };

  return (
    <div className="comment-item">
      <div className="flex gap-2 sm:gap-3">
        <div className="relative h-7 w-7 sm:h-8 sm:w-8 overflow-hidden rounded-full">
          {comment.avatarUrl ? (
            <Image
              src={comment.avatarUrl}
              alt={`${comment.firstName} ${comment.lastName}`}
              fill
              className="object-cover"
            />
          ) : (
            <DefaultAvatar
              name={`${comment.firstName} ${comment.lastName}`}
              size="100%"
            />
          )}
        </div>
        <div className="flex-1">
          <div className="rounded-lg bg-gray-100 p-2 sm:p-3">
            <div className="text-sm sm:text-base font-medium text-gray-800">
              {comment.firstName} {comment.lastName}
            </div>
            <div className="mt-0.5 sm:mt-1 text-sm sm:text-base text-gray-700">{comment.content}</div>
          </div>
          <div className="mt-1 flex flex-wrap items-center gap-2 sm:gap-4 text-xs text-gray-500">
            <button 
              onClick={async () => {
                if (comment.isLiked) {
                  await onUnlike(comment.commentPostSnId);
                } else {
                  await onLike(comment.commentPostSnId);
                }
                // Tải lại danh sách comment sau khi thích/bỏ thích
                if (showChildComments) {
                  setChildCommentsPage(0);
                  fetchChildComments(0);
                }
                onCommentUpdated();
              }}
              className={`${comment.isLiked ? 'text-blue-500 font-medium' : ''}`}
            >
              {comment.likeCount > 0 && `${comment.likeCount} `}Thích
            </button>
            <button 
              onClick={toggleReplyForm}
              className={`hover:text-gray-700 ${showChildComments ? 'text-blue-500 font-medium' : ''}`}
            >
              {showChildComments ? 'Ẩn trả lời' : 'Trả lời'}
            </button>
            <span>{formatDateForDisplay(comment.createdAt)}</span>
            <button 
              onClick={async () => {
                if (confirm("Bạn có chắc chắn muốn xóa bình luận này không?")) {
                  await onDelete(comment.commentPostSnId);
                  onCommentUpdated();
                }
              }}
              className="hover:text-red-500"
            >
              Xóa
            </button>
          </div>

          {/* Reply Form */}
          {showReplyForm && (
            <div className="mt-1.5 sm:mt-2 flex items-center gap-1.5 sm:gap-2">
              <input
                type="text"
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleAddReply();
                  }
                }}
                placeholder="Viết trả lời..."
                className="flex-1 rounded-full border border-gray-200 px-2 sm:px-3 py-1 text-xs sm:text-sm focus:outline-none focus:ring-1 focus:ring-blue-500/30"
              />
              <button
                onClick={handleAddReply}
                disabled={!replyContent.trim() || replyLoading}
                className="rounded-full bg-blue-500 p-1 sm:p-1.5 text-white hover:bg-blue-600 disabled:cursor-not-allowed disabled:opacity-50 transition-colors duration-200"
              >
                <FaPaperPlane size={12} />
              </button>
            </div>
          )}

          {/* Child Comments List */}
          {showChildComments && (
            <div className="mt-1.5 sm:mt-2 space-y-2 sm:space-y-3 pl-2 sm:pl-4 border-l-2 border-gray-100">
              {childComments.map((childComment) => (
                <div key={childComment.commentPostSnId} className="flex gap-1.5 sm:gap-2">
                  <div className="relative h-5 w-5 sm:h-6 sm:w-6 overflow-hidden rounded-full">
                    {childComment.avatarUrl ? (
                      <Image
                        src={childComment.avatarUrl}
                        alt={`${childComment.firstName} ${childComment.lastName}`}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <DefaultAvatar
                        name={`${childComment.firstName} ${childComment.lastName}`}
                        size="100%"
                      />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="rounded-lg bg-gray-100 p-1.5 sm:p-2">
                      <div className="text-xs sm:text-sm font-medium text-gray-800">
                        {childComment.firstName} {childComment.lastName}
                      </div>
                      <div className="mt-0.5 text-xs sm:text-sm text-gray-700">{childComment.content}</div>
                    </div>
                    <div className="mt-0.5 sm:mt-1 flex flex-wrap items-center gap-1.5 sm:gap-3 text-[10px] sm:text-xs text-gray-500">
                      <button 
                        onClick={async () => {
                          if (childComment.isLiked) {
                            await onUnlike(childComment.commentPostSnId);
                          } else {
                            await onLike(childComment.commentPostSnId);
                          }
                          // Tải lại danh sách comment con sau khi thích/bỏ thích
                          setChildCommentsPage(0);
                          fetchChildComments(0);
                          onCommentUpdated();
                        }}
                        className={`${childComment.isLiked ? 'text-blue-500 font-medium' : ''}`}
                      >
                        {childComment.likeCount > 0 && `${childComment.likeCount} `}Thích
                      </button>
                      <span>{formatDateForDisplay(childComment.createdAt)}</span>
                      <button 
                        onClick={async () => {
                          if (confirm("Bạn có chắc chắn muốn xóa bình luận này không?")) {
                            await onDelete(childComment.commentPostSnId);
                            // Tải lại danh sách comment con sau khi xóa
                            setChildCommentsPage(0);
                            fetchChildComments(0);
                            onCommentUpdated();
                          }
                        }}
                        className="hover:text-red-500"
                      >
                        Xóa
                      </button>
                    </div>
                  </div>
                </div>
              ))}
              
              {hasMoreChildComments && (
                <button 
                  onClick={() => fetchChildComments(childCommentsPage)}
                  className="w-full py-0.5 sm:py-1 text-center text-[10px] sm:text-xs text-blue-500 hover:text-blue-600 hover:underline"
                  disabled={childCommentsLoading}
                >
                  {childCommentsLoading ? 'Đang tải...' : 'Xem thêm trả lời'}
                </button>
              )}

            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CommentItem;
